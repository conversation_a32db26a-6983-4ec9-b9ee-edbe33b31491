@router.get("/by-project/{project_name}", response_model=List[Prompt])
async def list_prompts_by_project(
    project_name: str,
    skip: int = 0,
    limit: int = 100,
    current_user: UserInDB = Depends(get_current_active_user)
):
    """Get prompts filtered by project name"""
    cursor = mongodb.get_collection("prompts").find(
        {
            "$and": [
                {"$or": [
                    {"created_by": current_user.email},
                    {"is_public": True}
                ]},
                {"project": project_name}  # Add project filter
            ]
        }
    ).skip(skip).limit(limit)
    
    prompts = []
    async for document in cursor:
        # Handle created_by transformation for existing data
        created_by_data = document.get("created_by")
        if isinstance(created_by_data, dict) and "email" in created_by_data:
            document["created_by"] = created_by_data["email"]
        elif not isinstance(created_by_data, str):
            document["created_by"] = None

        # Ensure required fields exist
        if "average_tokens" not in document or document["average_tokens"] is None:
            document["average_tokens"] = 0.0
        if "tags" not in document or document["tags"] is None:
            document["tags"] = []
        elif not isinstance(document["tags"], list):
            document["tags"] = []

        document["id"] = str(document.pop("_id"))
        prompts.append(Prompt(**document))
    
    return prompts